import { BadRequestException, Injectable, Logger, OnApplicationBootstrap } from '@nestjs/common';
import { Cron } from '@nestjs/schedule';
import { <PERSON>ronJob } from 'cron';
import { UserService } from '../user/user.service';
import { EnergyService } from '../user/energy.service';
import { AutoMode } from 'src/user/enums/auto-mode.enum';

export interface AutoActionHandler {
  executeAction(userId: number, targetId: string, energyAmount: number): Promise<void>;
  checkActionValidity(userId: number, targetId: string): Promise<boolean>;
  saveAutoSettings(userId: number, targetId: string): Promise<void>;
  removeAutoSettings(userId: number, targetId: string): Promise<void>;
  getActiveAutoSettings(): Promise<{userId: number, targetId: string}[]>;
}

@Injectable()
export class AutoActionService implements OnApplicationBootstrap {
  // Changed to 30 minutes since energy regenerates every 30 minutes
  private readonly AUTO_INTERVAL = '*/30 * * * *'; // Every 30 minutes
  private readonly MINIMUM_ENERGY_THRESHOLD = 10; // Minimum energy required to execute action
  private autoActions = new Map<string, CronJob>(); // userId_targetId_type -> CronJob
  private actionHandlers = new Map<AutoMode, AutoActionHandler>();
  private readonly logger = new Logger(AutoActionService.name);

  constructor(
    private userService: UserService,
    private readonly energyService: EnergyService,
  ) {}

  /**
   * Called after the application has fully started.
   * This is where we restore auto actions that were active before server restart.
   */
  async onApplicationBootstrap() {
    this.logger.log('AutoActionService bootstrap - waiting for all handlers to register...');

    // Wait a bit for all handlers to register during module initialization
    setTimeout(async () => {
      try {
        await this.restoreAutoActions();
        this.logger.log('Auto actions restoration completed');
      } catch (error) {
        this.logger.error(`Error during auto actions restoration: ${error.message}`, error.stack);
      }
    }, 5000); // Wait 5 seconds for all modules to initialize
  }

  registerHandler(type: AutoMode, handler: AutoActionHandler) {
    this.actionHandlers.set(type, handler);
    this.logger.log(`Registered handler for auto action type: ${type}`);
  }

  async startAutoAction(
    userId: number,
    targetId: string,
    type: AutoMode,
  ) {
    const user = await this.userService.findOne(userId);
    if (!user.isPremium) {
      throw new BadRequestException('Auto mode is only available for premium users');
    }


    const handler = this.actionHandlers.get(type);
    if (!handler) {
      throw new BadRequestException(`No handler registered for auto action type: ${type}`);
    }

    const key = `${userId}_${targetId}_${type}`;
    if (this.autoActions.has(key)) {
      throw new BadRequestException(`Auto action already running for this ${type}`);
    }

    // Set expiration time - 24 hours for work, null for war (war has its own end time)
    let expiresAt: Date | null = null;
    if (type === 'work') {
      expiresAt = new Date();
      expiresAt.setHours(expiresAt.getHours() + 24);
    }

    // Convert string type to enum
    let autoMode: AutoMode;
    if (type === 'war') {
      autoMode = AutoMode.WAR;
    } else if (type === 'work') {
      autoMode = AutoMode.WORK;
    } else {
      autoMode = AutoMode.NONE;
    }

    // Update user with auto mode info
    await this.userService.updateAutoMode(userId, {
      activeAutoMode: autoMode,
      autoTargetId: targetId,
      autoModeExpiresAt: expiresAt
    });

    // Save auto settings using the handler
    await handler.saveAutoSettings(userId, targetId);

    // Execute the action immediately with current energy (if meets minimum threshold)
    try {
      // Get the user with updated energy
      const updatedUser = await this.energyService.updateEnergyById(userId);

      // Check if user has minimum required energy
      if (updatedUser.energy >= this.MINIMUM_ENERGY_THRESHOLD) {
        // Use all available energy for initial execution
        const initialEnergyToSpend = updatedUser.energy;

        this.logger.log(
          `Executing initial auto ${type} for user ${userId} on target ${targetId} with ${initialEnergyToSpend} energy`,
        );

        // Execute the action with the calculated energy
        await handler.executeAction(userId, targetId, initialEnergyToSpend);

        this.logger.log(`Initial auto ${type} successful for user ${userId} on target ${targetId}`);
      } else {
        this.logger.log(
          `Skipping initial auto ${type} for user ${userId} on target ${targetId} - insufficient energy (${updatedUser.energy}, minimum required: ${this.MINIMUM_ENERGY_THRESHOLD})`,
        );
      }
    } catch (initialActionError) {
      this.logger.error(
        `Initial auto ${type} execution failed for user ${userId} on target ${targetId}: ${initialActionError.message}`,
      );

      // If the error is not about insufficient energy, stop the auto action
      if (!initialActionError.message.includes('Insufficient energy')) {
        await this.stopAutoAction(userId, targetId, type);
        return;
      }
    }

    // Start the cron job for subsequent executions
    const job = new CronJob(this.AUTO_INTERVAL, async () => {
      try {
        // Check if auto mode has expired (for work mode)
        if (autoMode === AutoMode.WORK) {
          const user = await this.userService.findOne(userId);
          if (user.autoModeExpiresAt && new Date() > user.autoModeExpiresAt) {
            this.logger.log(
              `Auto ${type} for user ${userId} has expired. Stopping.`
            );
            await this.stopAutoAction(userId, targetId, type);
            return;
          }
        }

        // Check if the action is still valid BEFORE consuming energy
        const isValid = await handler.checkActionValidity(userId, targetId);
        if (!isValid) {
          this.logger.log(
            `Auto ${type} for user ${userId} on target ${targetId} is no longer valid. Stopping auto action.`,
          );
          await this.stopAutoAction(userId, targetId, type);
          return;
        }

        // Get the user with updated energy
        const user = await this.energyService.updateEnergyById(userId);

        this.logger.log(
          `Auto ${type} check - User: ${userId}, Target: ${targetId}, Current energy: ${user.energy}, Minimum required: ${this.MINIMUM_ENERGY_THRESHOLD}`,
        );

        // Only execute if user has minimum required energy
        if (user.energy >= this.MINIMUM_ENERGY_THRESHOLD) {
          try {
            // Use all available energy
            const energyToSpend = user.energy;

            this.logger.log(
              `Auto ${type} for user ${userId} on target ${targetId} with ${energyToSpend} energy`,
            );

            // Execute the action with the calculated energy
            await handler.executeAction(userId, targetId, energyToSpend);

            this.logger.log(`Auto ${type} successful for user ${userId} on target ${targetId}`);
          } catch (actionError) {
            this.logger.error(
              `Auto ${type} execution failed for user ${userId} on target ${targetId}: ${actionError.message}`,
            );

            // If the error is not about insufficient energy, stop the auto action
            if (!actionError.message.includes('Insufficient energy')) {
              await this.stopAutoAction(userId, targetId, type);
            }
          }
        } else {
          this.logger.log(
            `Skipping auto ${type} for user ${userId} on target ${targetId} - insufficient energy (${user.energy}, minimum required: ${this.MINIMUM_ENERGY_THRESHOLD})`,
          );
        }
      } catch (error) {
        // Don't stop auto action on temporary errors, just log them
        this.logger.error(
          `Error in auto ${type} for user ${userId} on target ${targetId}: ${error.message}`,
        );
        // Only stop if it's a critical error
        if (error.message.includes('not found') || error.message.includes('does not exist')) {
          await this.stopAutoAction(userId, targetId, type);
        }
      }
    });

    this.autoActions.set(key, job);
    job.start();

    this.logger.log(
      `Started auto ${type} for user ${userId} on target ${targetId} (will check every 30 minutes)`,
    );
  }

  async stopAutoAction(userId: number, targetId: string, type: AutoMode) {
    const key = `${userId}_${targetId}_${type}`;
    const job = this.autoActions.get(key);
    this.logger.log(`Stopping auto ${type} for user ${userId} on target ${targetId}`);

    // If job exists, stop it and remove from map
    if (job) {
      job.stop();
      this.autoActions.delete(key);
      this.logger.log(`Stopped job for key: ${key}`);
    } else {
      this.logger.warn(`No active job found for key: ${key}, but will still clean up user settings`);
    }

    try {
      // Always clear user's auto mode status, even if job wasn't found
      await this.userService.updateAutoMode(userId, {
        activeAutoMode: AutoMode.NONE,
        autoTargetId: null,
        autoModeExpiresAt: null
      });
      this.logger.log(`Cleared auto mode status for user ${userId}`);

      // Always try to remove auto settings using the handler
      const handler = this.actionHandlers.get(type);
      if (handler) {
        await handler.removeAutoSettings(userId, targetId);
        this.logger.log(`Removed auto settings for user ${userId} on target ${targetId}`);
      } else {
        this.logger.warn(`No handler found for auto action type: ${type}`);
      }
    } catch (error) {
      this.logger.error(`Error cleaning up auto mode for user ${userId}: ${error.message}`);
    }
  }

  // Restore auto actions on application startup
  async restoreAutoActions() {
    this.logger.log('Restoring auto actions...');

    for (const [type, handler] of this.actionHandlers.entries()) {
      try {
        const activeSettings = await handler.getActiveAutoSettings();

        this.logger.log(`Found ${activeSettings.length} active ${type} auto settings to restore`);

        for (const setting of activeSettings) {
          try {
            // For work mode, check if it has expired before restoring
            if (type === AutoMode.WORK) {
              const user = await this.userService.findOne(setting.userId);
              if (user.autoModeExpiresAt && new Date() > user.autoModeExpiresAt) {
                this.logger.log(
                  `Auto work for user ${setting.userId} has expired, removing settings`
                );

                // Remove the expired settings
                await handler.removeAutoSettings(setting.userId, setting.targetId);

                // Clear user's auto mode status
                await this.userService.updateAutoMode(setting.userId, {
                  activeAutoMode: AutoMode.NONE,
                  autoTargetId: null,
                  autoModeExpiresAt: null
                });
                continue;
              }
            }

            // Check if the action is still valid before restoring
            const isValid = await handler.checkActionValidity(
              setting.userId,
              setting.targetId
            );

            if (isValid) {
              await this.startAutoAction(
                setting.userId,
                setting.targetId,
                type
              );

              this.logger.log(
                `Restored auto ${type} for user ${setting.userId} on target ${setting.targetId}`
              );
            } else {
              this.logger.log(
                `Auto ${type} for user ${setting.userId} on target ${setting.targetId} is no longer valid, removing settings`
              );

              // Remove the settings if the action is no longer valid
              await handler.removeAutoSettings(setting.userId, setting.targetId);

              // Clear user's auto mode status
              await this.userService.updateAutoMode(setting.userId, {
                activeAutoMode: AutoMode.NONE,
                autoTargetId: null,
                autoModeExpiresAt: null
              });
            }
          } catch (error) {
            this.logger.error(
              `Failed to restore auto ${type} for user ${setting.userId} on target ${setting.targetId}: ${error.message}`
            );

            // Remove the settings if we can't restore it
            try {
              await handler.removeAutoSettings(setting.userId, setting.targetId);

              // Clear user's auto mode status
              await this.userService.updateAutoMode(setting.userId, {
                activeAutoMode: AutoMode.NONE,
                autoTargetId: null,
                autoModeExpiresAt: null
              });
            } catch (cleanupError) {
              this.logger.error(
                `Failed to clean up auto ${type} settings for user ${setting.userId}: ${cleanupError.message}`
              );
            }
          }
        }
      } catch (error) {
        this.logger.error(
          `Error restoring ${type} auto actions: ${error.message}`
        );
      }
    }
  }

  // Check for expired auto actions every hour
  @Cron('0 * * * *')
  async cleanupExpiredAutoActions() {
    this.logger.log('Cleaning up expired auto actions...');
    this.logger.log(`Current active auto actions: ${this.autoActions.size}`);
    // Log all active keys for debugging
    for (const key of this.autoActions.keys()) {
      this.logger.log(`Active auto action: ${key}`);
    }
    for (const [type, handler] of this.actionHandlers.entries()) {
      try {
        // Get all active settings
        const activeSettings = await handler.getActiveAutoSettings();

        this.logger.log(`Checking ${activeSettings.length} active ${type} auto settings for expiration`);

        // Check each setting for validity
        for (const setting of activeSettings) {
          try {
            // For work mode, check expiration first
            if (type === 'work') {
              const user = await this.userService.findOne(setting.userId);
              if (user.autoModeExpiresAt && new Date() > user.autoModeExpiresAt) {
                this.logger.log(
                  `Auto work for user ${setting.userId} has expired. Stopping.`
                );

                // Stop the auto action if it's running
                await this.stopAutoAction(
                  setting.userId,
                  setting.targetId,
                  type
                );
                continue;
              }
            }

            const isValid = await handler.checkActionValidity(
              setting.userId,
              setting.targetId
            );

            if (!isValid) {
              this.logger.log(
                `Auto ${type} for user ${setting.userId} on target ${setting.targetId} is no longer valid. Stopping.`
              );

              // Stop the auto action if it's running
              await this.stopAutoAction(
                setting.userId,
                setting.targetId,
                type
              );
            }
          } catch (error) {
            this.logger.error(
              `Error checking validity of ${type} auto action for user ${setting.userId}: ${error.message}`
            );
          }
        }
      } catch (error) {
        this.logger.error(
          `Error cleaning up expired ${type} auto actions: ${error.message}`
        );
      }
    }
  }

  // Add this method to expose the autoActions map (for debugging only)
  getAutoActions(): Map<string, CronJob> {
    return this.autoActions;
  }
}


