import {
  Injectable,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Factory, FactoryType } from './entity/factory.entity';
import { User } from '../user/entity/user.entity';
import { WorkSession } from '../work-session/entity/work-session.entity';
import {
  CreateFactoryDto,
  UpdateFactoryDto,
  WorkAtFactoryDto,
} from './dto/factory.dto';
import { UserService } from 'src/user/user.service';
import { RegionService } from 'src/region/region.service';
import { EnergyService } from 'src/user/energy.service';

@Injectable()
export class FactoryService {
  constructor(
    @InjectRepository(Factory)
    private factoryRepository: Repository<Factory>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @InjectRepository(WorkSession)
    private workSessionRepository: Repository<WorkSession>,
    private regionService: RegionService,
    private userService: UserService,
    private energyService: EnergyService
  ) {}

  async create(
    createFactoryDto: CreateFactoryDto,
    userId: number,
  ): Promise<Factory> {
    const user = await this.userRepository.findOne({ where: { id: userId } });
    if (!user) {
      throw new NotFoundException('User not found');
    }

    const factory = this.factoryRepository.create({
      ...createFactoryDto,
      ownerId: userId,
    });

    return this.factoryRepository.save(factory);
  }

  async findAll(regionId?: string): Promise<Factory[]> {
    const queryOptions: any = {
      relations: ['owner', 'region', 'workers'],
    };

    if (regionId) {
      queryOptions.where = { regionId };
    }

    return this.factoryRepository.find(queryOptions);
  }

  async findOne(id: number): Promise<Factory> {
    const factory = await this.factoryRepository.findOne({
      where: { id },
      relations: ['owner', 'region', 'workSessions', 'workSessions.worker', 'workers'],
    });

    if (!factory) {
      throw new NotFoundException(`Factory with ID ${id} not found`);
    }

    return factory;
  }

  async update(
    id: number,
    updateFactoryDto: UpdateFactoryDto,
  ): Promise<Factory> {
    const factory = await this.findOne(id);
    Object.assign(factory, updateFactoryDto);
    return this.factoryRepository.save(factory);
  }

  async workAtFactory(
    id: number,
    userId: number,
    workAtFactoryDto: WorkAtFactoryDto,
  ): Promise<{workSession: WorkSession, user: User}> {
    const factory = await this.findOne(id);

    // Use a transaction to ensure atomicity and prevent race conditions
    return await this.userRepository.manager.transaction(async (transactionManager) => {
      // Get user with pessimistic locking - NO RELATIONS to avoid FOR UPDATE issues
      const user = await transactionManager.findOne(User, {
        where: { id: userId },
        lock: { mode: 'pessimistic_write' }
      });

      if (!user) {
        throw new NotFoundException(`User with ID ${userId} not found`);
      }

      // Load all required relations separately (no locking issues)
      const userWithRelations = await transactionManager.findOne(User, {
        where: { id: userId },
        relations: ['region', 'region.state', 'workingAt']
      });

      if (userWithRelations) {
        user.region = userWithRelations.region;
        user.workingAt = userWithRelations.workingAt;
      }

      // DON'T update energy here - AutoActionService already calculated the correct energy
      // For manual work, energy should be updated by the controller before calling this method

      // Only check max workers if the user is not already working at this factory
      if (!user.workingAt || user.workingAt.id !== factory.id) {
        // Check if factory has reached maximum worker capacity
        const currentWorkerCount = await this.getWorkerCount(factory.id);
        if (currentWorkerCount >= factory.maxWorkers) {
          throw new BadRequestException(`Factory has reached maximum capacity of ${factory.maxWorkers} workers`);
        }
      }

      if(user.isTraveling){
        throw new BadRequestException('You are already traveling, cannot work at factory!');
      }

      // Check if user has enough energy
      if (user.energy < workAtFactoryDto.energySpent) {
        throw new BadRequestException(`Insufficient energy: User has ${user.energy} but needs ${workAtFactoryDto.energySpent}`);
      }

      // Log energy consumption for debugging
      console.log(
        `[WORK] Before consumption - User ID: ${userId}, Energy: ${user.energy}, Amount to consume: ${workAtFactoryDto.energySpent}`,
      );

      // Consume energy directly (no additional service calls to prevent race conditions)
      user.energy -= workAtFactoryDto.energySpent;

      // Log energy after consumption
      console.log(
        `[WORK] After consumption - User ID: ${userId}, Remaining energy: ${user.energy}`,
      );

      // Grant XP for working (1 XP per energy spent)
      user.experience += workAtFactoryDto.energySpent;
      user.checkAndUpdateLevel();

      // Simplified work formula based on Rival Regions
      const enduranceMultiplier = 1 + (user.endurance || 10) / 20;
      const educationMultiplier = 1 + (user.intelligence || 1) / 5;
      const levelMultiplier = 1 + (user.level || 1) / 5;

      // Calculate total work efficiency
      const efficiencyMultiplier = enduranceMultiplier * educationMultiplier * levelMultiplier;

      // Calculate wage earnings (money)
      const wageEarned = Math.floor(workAtFactoryDto.energySpent * factory.wage * efficiencyMultiplier);

      // Calculate resource earnings
      let resourceEarned = 0;

      // Always give wage as money
      user.money += wageEarned;

      // Give resources based on factory type
      switch (factory.type) {
        case FactoryType.GOLD:
          // For gold, give exactly 1 gold per 10 energy (rounded down), no multipliers
          const goldEarned = Math.floor(workAtFactoryDto.energySpent / 10);
          user.gold += goldEarned;
          resourceEarned = goldEarned;
          break;
        case FactoryType.MONEY:
          // For money factories, apply the full formula with multipliers
          resourceEarned = Math.floor(workAtFactoryDto.energySpent * factory.resourcePerWork * efficiencyMultiplier);
          user.money += resourceEarned;
          break;
        // Add cases for future resource types
      }

      // Set the user's working factory
      user.workingAt = factory;

      // Save user within the transaction
      const savedUser = await transactionManager.save(User, user);

      // Create and save work session within the transaction
      const workSession = transactionManager.create(WorkSession, {
        factoryId: factory.id,
        workerId: user.id,
        energySpent: workAtFactoryDto.energySpent,
        wageEarned,
        resourceEarned,
        efficiencyMultiplier,
        resourceType: factory.type,
      });

      const savedWorkSession = await transactionManager.save(WorkSession, workSession);

      return { workSession: savedWorkSession, user: savedUser };
    });
  }

  async getWorkHistory(userId: number): Promise<WorkSession[]> {
    return this.workSessionRepository.find({
      where: { workerId: userId },
      relations: ['factory'],
      order: { createdAt: 'DESC' },
    });
  }

  async createDefaultFactoriesForRegion(regionId: string): Promise<Factory[]> {
    const region = await this.regionService.findById(regionId);

    // Create a default gold factory for the region
    const goldFactory = this.factoryRepository.create({
      name: `${region.name} Gold Mine`,
      type: FactoryType.GOLD,
      regionId,
      ownerId: null, // State-owned factory
      wage: 1, // Default wage
      maxWorkers: 100, // Default max workers
      energyCost: 10, // Default energy cost
      resourcePerWork: 1.0, // Default resource per work
    });

    await this.factoryRepository.save(goldFactory);
    return [goldFactory];
  }

  async createDefaultFactoriesForAllRegions(): Promise<number> {
    const regions = await this.regionService.findAll();
    let factoriesCreated = 0;

    for (const region of regions) {
      // Check if region already has factories
      const existingFactories = await this.factoryRepository.count({
        where: { regionId: region.id }
      });

      if (existingFactories === 0) {
        const factories = await this.createDefaultFactoriesForRegion(region.id);
        factoriesCreated += factories.length;
      }
    }

    return factoriesCreated;
  }

  async getUserWithRegion(userId: number): Promise<User> {
    const user = await this.userRepository.findOne({
      where: { id: userId },
      relations: ['region'],
    });

    if (!user) {
      throw new NotFoundException(`User with ID ${userId} not found`);
    }

    return user;
  }

  async stopWorkingAtFactory(userId: number): Promise<User> {
    const user = await this.userRepository.findOne({
      where: { id: userId },
      relations: ['workingAt']
    });

    if (!user) {
      throw new NotFoundException(`User with ID ${userId} not found`);
    }

    if (!user.workingAt) {
      throw new BadRequestException('User is not currently working at any factory');
    }

    user.workingAt = null;
    return this.userRepository.save(user);
  }

  async getWorkerCount(factoryId: number): Promise<number> {
    return this.userRepository.count({
      where: { workingAt: { id: factoryId } }
    });
  }
}









