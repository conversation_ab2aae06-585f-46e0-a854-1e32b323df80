import { Controller, Get, Post, Body, Patch, Param, Req, Query } from '@nestjs/common';
import { FactoryService } from './factory.service';
import {
  CreateFactoryDto,
  UpdateFactoryDto,
  WorkAtFactoryDto,
  AutoWorkDto,
} from './dto/factory.dto';
import { WorkAutoService } from './work-auto.service';
import { Request } from 'express';
import { ApiTags, ApiOperation, ApiResponse, ApiQuery } from '@nestjs/swagger';
import { UserService } from 'src/user/user.service';
import { AutoActionService } from 'src/shared/auto-action.service';
import { EnergyService } from 'src/user/energy.service';

interface RequestWithUser extends Request {
  user: {
    userId: number;
  };
}

@ApiTags('Factories')
@Controller('factories')
export class FactoryController {
  constructor(
    private readonly factoryService: FactoryService,
    private readonly workAutoService: WorkAutoService,
    private readonly userService: UserService,
    private readonly autoActionService: AutoActionService,
    private readonly energyService: EnergyService,
  ) {}

  @Post()
  create(
    @Body() createFactoryDto: CreateFactoryDto,
    @Req() req: RequestWithUser,
  ) {
    return this.factoryService.create(createFactoryDto, req.user.userId);
  }

  @Get('work-history')
  getWorkHistory(@Req() req: RequestWithUser) {
    return this.factoryService.getWorkHistory(req.user.userId);
  }

  @Get('user-region')
  @ApiOperation({ summary: 'Get factories in the current user\'s region' })
  async getFactoriesInUserRegion(@Req() req: RequestWithUser) {
    const user = await this.factoryService.getUserWithRegion(req.user.userId);
    return this.factoryService.findAll(user.region.id);
  }

  @Get()
  @ApiOperation({ summary: 'Get all factories, optionally filtered by region' })
  @ApiQuery({
    name: 'regionId',
    required: false,
    description: 'Filter factories by region ID',
    type: String,
  })
  findAll(@Query('regionId') regionId?: string) {
    return this.factoryService.findAll(regionId);
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.factoryService.findOne(+id);
  }

  @Patch(':id')
  update(@Param('id') id: string, @Body() updateFactoryDto: UpdateFactoryDto) {
    return this.factoryService.update(+id, updateFactoryDto);
  }

  @Post(':id/work')
  async workAtFactory(
    @Param('id') id: string,
    @Body() workAtFactoryDto: WorkAtFactoryDto,
    @Req() req: RequestWithUser,
  ) {
    // For manual work requests, update user energy first
    await this.energyService.updateEnergyById(req.user.userId);

    return this.factoryService.workAtFactory(
      +id,
      req.user.userId,
      workAtFactoryDto,
    );
  }

  @Post(':id/auto-work')
  @ApiOperation({ summary: 'Enable or disable auto work mode for a factory' })
  @ApiResponse({ status: 200, description: 'Auto work mode updated successfully' })
  async toggleAutoWork(
    @Param('id') id: string,
    @Body() autoWorkDto: AutoWorkDto,
    @Req() req: RequestWithUser,
  ) {
    if (autoWorkDto.enable) {
      await this.workAutoService.startAutoWork(req.user.userId, id);
      return { message: 'Auto work mode enabled successfully' };
    } else {
      await this.workAutoService.stopAutoWork(req.user.userId, id);
      return { message: 'Auto work mode disabled successfully' };
    }
  }

  // @Post('seed-default')
  // @ApiOperation({ summary: 'Create default factories for all regions' })
  // @ApiResponse({ status: 201, description: 'Default factories created successfully' })
  // async seedDefaultFactories() {
  //   const factoriesCreated = await this.factoryService.createDefaultFactoriesForAllRegions();
  //   return { message: `Created ${factoriesCreated} default factories` };
  // }

  @Post('region/:regionId/default-factories')
  @ApiOperation({ summary: 'Create default factories for a specific region' })
  @ApiResponse({ status: 201, description: 'Default factories created for region' })
  async createDefaultFactoriesForRegion(@Param('regionId') regionId: string) {
    const factories = await this.factoryService.createDefaultFactoriesForRegion(regionId);
    return {
      message: `Created ${factories.length} default factories for region`,
      factories
    };
  }

  @Get('auto-work/status')
  @ApiOperation({ summary: 'Get auto work status for current user' })
  async getAutoWorkStatus(@Req() req: RequestWithUser) {
    const userId = req.user.userId;
    const user = await this.userService.findOne(userId);

    // Get all active auto work jobs for this user
    const activeJobs = Array.from(this.autoActionService.getAutoActions().entries())
      .filter(([key]) => key.startsWith(`${userId}_`) && key.endsWith('_work'))
      .map(([key]) => {
        const [userId, targetId, type] = key.split('_');
        return { userId, targetId, type };
      });

    return {
      userId,
      isPremium: user.isPremium,
      activeAutoMode: user.activeAutoMode,
      autoTargetId: user.autoTargetId,
      autoModeExpiresAt: user.autoModeExpiresAt,
      timeUntilExpiration: user.autoModeExpiresAt ?
        Math.floor((new Date(user.autoModeExpiresAt).getTime() - Date.now()) / 1000) : null,
      activeJobs
    };
  }
}


